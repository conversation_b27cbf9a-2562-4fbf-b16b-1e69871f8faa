// Common Route for each version where we categorize it by modules
const express = require('express');
const app = express();
const { validationErrorHandler } = require('../../middlewares/validations/validationErrorHandler.js');

const auth = require('./authRoute.js');
const category = require('./categoryRoute.js');
const component = require('./componentRoute.js');
const utility = require('./utilityRoutes.js');
const modifier = require('./modifierRoute.js');
const section = require('./sectionRoute.js');
const tags = require('./tagRoute.js');
const supportedPlatforms = require('./supportedPlatformsRoute.js');
const migrations = require('./migrationRoute.js');
const difficultyLevels = require('./difficultyLevelsRoute.js');
const emailTemplates = require('../cms/emailTemplatesRoute.js');
const users = require('../cms/usersRoute.js');
const adminRoles = require('../cms/adminRolesRoute.js');
const adminPermissionGroups = require('../cms/adminPermissionGroupRoute.js');
const adminPermissions = require('../cms/adminPermissionRoute.js');
const topUpPlans = require('../cms/topUpPlansRoute.js');
const admins = require('./adminRoute.js');
const staticPages = require('./staticPagesRoute.js');
const mpPoints = require('./mpPointsRoute.js');
const mpcBonusRequest = require('./mpcBonusRequestRoute.js');
const templatesRoutes = require('./templatesRoute.js');
const settings = require('./settingsRoute.js');
const repository = require('./repositoryRoute.js');
const codeSpace = require('./codeSpaceRoute.js');
const collection = require('./collectionRoute.js');
const elements = require('./elementRoute.js');
const license = require('./platformLicensesRoute.js');
const globalItemSettings = require('./globalItemSettingsRoute.js');

// route grouping without token
app.prefix('/auth', function (router) { // Any route    
    router.use('/', auth);
});

app.prefix('/v1/category', function (router) { // Any route    
    router.use('/', category);
});

app.prefix('/v1/component', function (router) { // Any route    
    router.use('/', component);
});

app.prefix('/v1/utility', function (router) { // Any route    
    router.use('/', utility);
});

app.prefix('/v1/modifier', function (router) { // Any route    
    router.use('/', modifier);
});

app.prefix('/v1/section', function (router) { // Any route    
    router.use('/', section);
});

app.prefix('/v1/tags', function (router) { // Any route    
    router.use('/', tags);
});

app.prefix('/v1/supported-platforms', function (router) { // Any route    
    router.use('/', supportedPlatforms);
});

app.prefix('/v1/migrations', function (router) { // Any route    
    router.use('/', migrations);
});

app.prefix('/v1/difficulty-levels', function (router) { // Any route    
    router.use('/', difficultyLevels);
});

app.prefix('/v1/email-templates', function (router) { // Any route  
    router.use('/', emailTemplates);
});

app.prefix('/v1/users', function (router) { // Any route  
    router.use('/', users);
});

app.prefix('/v1/admin-roles', function (router) { // Any route  
    router.use('/', adminRoles);
});

app.prefix('/v1/admin-permission-groups', function (router) { // Any route  
    router.use('/', adminPermissionGroups);
});

app.prefix('/v1/admin-permissions', function (router) { // Any route  
    router.use('/', adminPermissions);
});

app.prefix('/v1/top-up-plans', function (router) { // Any route  
    router.use('/', topUpPlans);
});

app.prefix('/v1/admins', function (router) { // Any route  
    router.use('/', admins);
});

app.prefix('/v1/pages', function (router) { // Any route  
    router.use('/', staticPages);
});

app.prefix('/v1/points', function (router) { // Any route  
    router.use('/', mpPoints);
});

app.prefix('/v1/mpc-bonus-request', function (router) { // Any route  
    router.use('/', mpcBonusRequest);
});

app.prefix('/v1/templates', function (router) { // Any route  
    router.use('/', templatesRoutes);
});

app.prefix('/v1/settings', function (router) {
    router.use('/', settings);
});

app.prefix('/v1/repository', function (router) {
    router.use('/', repository);
});

app.prefix('/v1/code-space', function (router) { // Any route    
    router.use('/', codeSpace);
});

app.prefix('/v1/collections', function (router) { // Any route    
    router.use('/', collection);
});

app.prefix('/v1/elements', function (router) { // Any route    
    router.use('/', elements);
});

app.prefix('/v1/license', function (router) { // Any route    
    router.use('/', license);
});

app.prefix('/v1/global-item-settings', function (router) { // Any route    
    router.use('/', globalItemSettings);
});
// validation handler
app.use(validationErrorHandler);
module.exports = app;