const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentPrivateShareSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components',
        required: true
    },
    shared_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    shared_with_email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true
    },
    shared_with_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        default: null
    },
    access_token: {
        type: String,
        required: true,
        unique: true
    },
    status: {
        type: String,
        enum: ['pending', 'accepted', 'expired', 'revoked'],
        default: 'pending'
    },
    expires_at: {
        type: Date,
        required: true
    },
    accessed_at: {
        type: Date
    },
    access_count: {
        type: Number,
        default: 0
    },
    personal_message: {
        type: String,
        maxlength: 500
    },
    is_active: {
        type: Boolean,
        default: true
    },
    revoked_at: {
        type: Date
    },
    revoked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    accepted_at: {
        type: Date
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Compound indexes for performance
componentPrivateShareSchema.index({ component_id: 1, shared_with_email: 1 });
componentPrivateShareSchema.index({ shared_with_user: 1, status: 1, expires_at: 1 });
componentPrivateShareSchema.index({ access_token: 1 });
componentPrivateShareSchema.index({ shared_by: 1, status: 1 });
componentPrivateShareSchema.index({ expires_at: 1 }); // For cleanup jobs

// Pre-save middleware to set shared_with_user if email matches existing user
componentPrivateShareSchema.pre('save', async function(next) {
    if (this.isNew && !this.shared_with_user) {
        try {
            const Users = require('./users.model').Users;
            const existingUser = await Users.findOne({ 
                email: this.shared_with_email,
                is_active: true 
            }).select('_id').lean();
            
            if (existingUser) {
                this.shared_with_user = existingUser._id;
                this.status = 'accepted'; // Auto-accept for existing users
            }
        } catch (error) {
            // Continue without setting user if lookup fails
        }
    }
    next();
});

// Static method to check if user has access to component
componentPrivateShareSchema.statics.hasAccess = async function(componentId, userId, userEmail) {
    const query = {
        component_id: componentId,
        status: { $in: ['pending', 'accepted'] },
        expires_at: { $gt: new Date() },
        is_active: true
    };

    if (userId) {
        query.$or = [
            { shared_with_user: userId },
            { shared_with_email: userEmail, shared_with_user: null }
        ];
    } else if (userEmail) {
        query.shared_with_email = userEmail;
        query.shared_with_user = null;
    } else {
        return false;
    }

    const share = await this.findOne(query).lean();
    return !!share;
};

// Static method to link pending shares when user signs up
componentPrivateShareSchema.statics.linkPendingShares = async function(userId, email) {
    const now = new Date();
    const result = await this.updateMany(
        {
            shared_with_email: email,
            shared_with_user: null,
            status: 'pending',
            expires_at: { $gt: now },
            is_active: true
        },
        {
            $set: {
                shared_with_user: userId,
                status: 'accepted',
                accepted_at: now,
                updated_at: now
            }
        }
    );
    return result.modifiedCount;
};

// Static method to cleanup expired shares
componentPrivateShareSchema.statics.cleanupExpiredShares = async function() {
    const result = await this.updateMany(
        {
            expires_at: { $lt: new Date() },
            status: { $ne: 'expired' }
        },
        {
            status: 'expired'
        }
    );
    return result.modifiedCount;
};

const ComponentPrivateShares = mongoose.model('component_private_shares', componentPrivateShareSchema);

module.exports = {
    ComponentPrivateShares
};