const Joi = require('joi');

const shareComponentPrivatelyValidation = (data) => {
    const schema = Joi.object({
        emails: Joi.array()
            .items(Joi.string().email().required())
            .min(1)
            .max(10)
            .required()
            .messages({
                'array.min': 'At least one email is required',
                'array.max': 'Maximum 10 emails allowed per share',
                'string.email': 'Please provide valid email addresses'
            }),
        personal_message: Joi.string()
            .max(500)
            .optional()
            .allow('')
            .messages({
                'string.max': 'Personal message cannot exceed 500 characters'
            }),
        expiration_days: Joi.number()
            .integer()
            .min(1)
            .max(365)
            .default(30)
            .optional()
            .messages({
                'number.min': 'Expiration must be at least 1 day',
                'number.max': 'Expiration cannot exceed 365 days'
            })
    });

    return schema.validate(data);
};

const getPrivateSharesValidation = (data) => {
    const schema = Joi.object({
        page: Joi.number()
            .integer()
            .min(1)
            .default(1)
            .optional(),
        limit: Joi.number()
            .integer()
            .min(1)
            .max(50)
            .default(10)
            .optional(),
        status: Joi.string()
            .valid('pending', 'accepted', 'expired', 'revoked')
            .optional()
    });

    return schema.validate(data);
};

const accessTokenValidation = (data) => {
    const schema = Joi.object({
        token: Joi.string()
            .length(64)
            .hex()
            .required()
            .messages({
                'string.length': 'Invalid access token format',
                'string.hex': 'Invalid access token format'
            })
    });

    return schema.validate(data);
};

module.exports = {
    shareComponentPrivatelyValidation,
    getPrivateSharesValidation,
    accessTokenValidation
};