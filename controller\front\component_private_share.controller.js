// Private Share Controller Functions
const mongoose = require('mongoose');

// Models
const { Components } = require('../../models/component.model');

// Services
const constants = require('../../config/constants');
const { ReS } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { fetchFullComponentData } = require('../../services/component.service');
const { 
    shareComponentPrivately, 
    acceptPrivateShare, 
    checkPrivateAccess, 
    getMyPrivateShares, 
    getComponentsSharedWithMe, 
    revokePrivateShare,
    getShareStatistics
} = require('../../services/private_share.service');

async function shareComponentPrivatelyController(req, res) {
    try {
        const componentId = req.params.id;
        const { emails, personal_message, expiration_days } = req.body;
        const userId = req.session._id;

        const result = await shareComponentPrivately(
            componentId,
            userId,
            emails,
            personal_message,
            expiration_days
        );

        return ReS(res, constants.success_code, 'Component shared successfully', result);
    } catch (error) {
        logger.error(`Error in shareComponentPrivatelyController: ${error.message}`);
        return ReS(res, constants.server_error_code, error.message);
    }
}

async function acceptPrivateShareController(req, res) {
    try {
        const { token } = req.params;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        const result = await acceptPrivateShare(token, userId, userEmail);

        if (result.requiresSignup) {
            return ReS(res, constants.success_code, 'Please sign up to access this component', {
                component: result.component,
                sharedBy: result.sharedBy,
                message: result.message,
                requiresSignup: true,
                signupUrl: `${process.env.SITE_URL}signup?redirect=${encodeURIComponent(`private-share/${token}`)}`
            });
        }

        return ReS(res, constants.success_code, 'Access granted successfully', result);
    } catch (error) {
        logger.error(`Error in acceptPrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyPrivateSharesController(req, res) {
    try {
        const userId = req.session._id;
        const options = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 10,
            status: req.query.status
        };

        const result = await getMyPrivateShares(userId, options);
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyPrivateSharesController: ${error.message}`);
        return ReS(res, constants.server_error_code, error.message);
    }
}

async function getComponentsSharedWithMeController(req, res) {
    try {
        const userId = req.session._id;
        const userEmail = req.session.email;
        const options = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 10,
            status: req.query.status
        };

        const result = await getComponentsSharedWithMe(userId, userEmail, options);
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMeController: ${error.message}`);
        return ReS(res, constants.server_error_code, error.message);
    }
}

async function revokePrivateShareController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await revokePrivateShare(shareId, userId);
        return ReS(res, constants.success_code, result.message);
    } catch (error) {
        logger.error(`Error in revokePrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        const stats = await getShareStatistics(userId);
        return ReS(res, constants.success_code, 'Statistics fetched successfully', stats);
    } catch (error) {
        logger.error(`Error in getShareStatisticsController: ${error.message}`);
        return ReS(res, constants.server_error_code, error.message);
    }
}

async function getComponentWithPrivateAccess(req, res) {
    try {
        const componentSlug = req.params.slug;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        // Find the component
        const component = await Components.findOne({
            slug: componentSlug
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Component not found');
        }

        // Check access
        const accessInfo = await checkPrivateAccess(component._id, userId, userEmail);

        if (!accessInfo.hasAccess) {
            return ReS(res, constants.forbidden_code, 'You do not have access to this component');
        }

        // If user has access, fetch full component data
        const componentData = await fetchFullComponentData(componentSlug, component.is_paid, null, userId);
        
        // Add access type to response
        componentData.access_type = accessInfo.accessType;

        return ReS(res, constants.success_code, 'Data fetched successfully', componentData);
    } catch (error) {
        logger.error(`Error in getComponentWithPrivateAccess: ${error.message}`);
        return ReS(res, constants.server_error_code, error.message);
    }
}

module.exports = {
    shareComponentPrivatelyController,
    acceptPrivateShareController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    getComponentWithPrivateAccess
};