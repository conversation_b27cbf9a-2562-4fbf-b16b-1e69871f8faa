const express = require('express');
const router = express.Router();
const authRouter = express.Router();

// Controllers
const {
    shareComponentPrivatelyController,
    acceptPrivateShareController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    getComponentWithPrivateAccess
} = require('../../controller/front/component_private_share.controller');

// Middleware
const { validateShareComponentPrivately, validateGetPrivateShares, validateAccessToken } = require('../../middlewares/validatePrivateShare');
const { validateComponentOwnership } = require('../../middlewares/validatePrivateAccess');

// Public routes (no authentication required)
// Accept private share invitation
router.get('/private-share/:token', validateAccessToken, acceptPrivateShareController);

// Get component with private access (for public access)
router.get('/private/:slug', getComponentWithPrivateAccess);

// Authenticated routes
// Share component privately
authRouter.post('/:id/share-privately', validateComponentOwnership, validateShareComponentPrivately, shareComponentPrivatelyController);

// Get my private shares
authRouter.get('/my-private-shares', validateGetPrivateShares, getMyPrivateSharesController);

// Get components shared with me
authRouter.get('/shared-with-me', validateGetPrivateShares, getComponentsSharedWithMeController);

// Revoke private share
authRouter.delete('/private-share/:shareId/revoke', revokePrivateShareController);

// Get share statistics
authRouter.get('/share-statistics', getShareStatisticsController);

module.exports = {
    router,
    authRouter
};