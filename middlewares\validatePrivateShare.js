const { shareComponentPrivatelyValidation, getPrivateSharesValidation, accessTokenValidation } = require('../validations/front/component/privateShareValidation');
const { ReS } = require('../services/general.helper');
const constants = require('../config/constants');

const validateShareComponentPrivately = (req, res, next) => {
    const { error } = shareComponentPrivatelyValidation(req.body);
    if (error) {
        return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
    }
    next();
};

const validateGetPrivateShares = (req, res, next) => {
    const { error } = getPrivateSharesValidation(req.query);
    if (error) {
        return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
    }
    next();
};

const validateAccessToken = (req, res, next) => {
    const { error } = accessTokenValidation({ token: req.params.token });
    if (error) {
        return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
    }
    next();
};

module.exports = {
    validateShareComponentPrivately,
    validateGetPrivateShares,
    validateAccessToken
};