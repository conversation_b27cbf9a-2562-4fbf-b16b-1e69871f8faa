const mongoose = require('mongoose');
const { ComponentPrivateShares } = require('../models/component_private_shares.model');
const logger = require('../config/logger');

async function runMigration() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        logger.info('Migration: Starting to add accepted_at field to existing private shares');
        
        // Update all accepted shares that don't have accepted_at set
        const result = await ComponentPrivateShares.updateMany(
            { 
                status: 'accepted',
                accepted_at: { $exists: false },
                updated_at: { $exists: true }
            },
            [
                {
                    $set: {
                        accepted_at: '$updated_at'
                    }
                }
            ]
        );

        logger.info(`Migration: Successfully updated ${result.modifiedCount} private shares with accepted_at field`);
        
        // Close the connection
        await mongoose.connection.close();
        process.exit(0);
    } catch (error) {
        logger.error(`Migration failed: ${error.message}`, { error: error.stack });
        process.exit(1);
    }
}

runMigration();
